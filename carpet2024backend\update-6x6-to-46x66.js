const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function update6x6To46x66() {
  try {
    console.log('🔍 Updating Stock March entries from "6 X 6" to "4.6 X 6.6"...');
    
    // Find Stock March entries with "6 X 6" size
    const entries6x6 = await CarpetReceived.find({
      $and: [
        {
          $or: [
            { receiveNo: { $regex: '^H-', $options: 'i' } },
            { receiveNo: { $regex: '^March', $options: 'i' } },
            { weaverName: { $regex: 'stock march', $options: 'i' } }
          ]
        },
        { size: "6 X 6" }
      ]
    });
    
    console.log(`📊 Found ${entries6x6.length} entries to update`);
    
    let updatedCount = 0;
    
    for (const entry of entries6x6) {
      const updateData = {
        size: "4.6 X 6.6"
      };

      // Update issueNo object if it exists
      if (entry.issueNo) {
        updateData['issueNo.size.sizeInYard'] = "4.6 X 6.6";
        updateData['issueNo.size.sizeinMeter'] = "4.6 X 6.6";
      }

      await CarpetReceived.updateOne(
        { _id: entry._id },
        { $set: updateData }
      );

      updatedCount++;
      console.log(`✅ Updated: ${entry.receiveNo} - Size: "6 X 6" -> "4.6 X 6.6"`);
    }
    
    console.log(`🎉 Successfully updated ${updatedCount} entries from "6 X 6" to "4.6 X 6.6"`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

update6x6To46x66();
