const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function update1x2To111x211() {
  try {
    console.log('🔍 Updating Stock March entries from "1 X 2" to "1.11 X 2.11"...');
    
    // Find Stock March entries with "1 X 2" size
    const entries1x2 = await CarpetReceived.find({
      $and: [
        {
          $or: [
            { receiveNo: { $regex: '^H-', $options: 'i' } },
            { receiveNo: { $regex: '^March', $options: 'i' } },
            { weaverName: { $regex: 'stock march', $options: 'i' } }
          ]
        },
        { size: "1 X 2" }
      ]
    });
    
    console.log(`📊 Found ${entries1x2.length} entries to update`);
    
    let updatedCount = 0;
    
    for (const entry of entries1x2) {
      const updateData = {
        size: "1.11 X 2.11"
      };

      // Update issueNo object if it exists
      if (entry.issueNo) {
        updateData['issueNo.size.sizeInYard'] = "1.11 X 2.11";
        updateData['issueNo.size.sizeinMeter'] = "1.11 X 2.11";
      }

      await CarpetReceived.updateOne(
        { _id: entry._id },
        { $set: updateData }
      );

      updatedCount++;
      console.log(`✅ Updated: ${entry.receiveNo} - Size: "1 X 2" -> "1.11 X 2.11"`);
    }
    
    console.log(`🎉 Successfully updated ${updatedCount} entries from "1 X 2" to "1.11 X 2.11"`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

update1x2To111x211();
