const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function update3x1To31x11() {
  try {
    console.log('🔍 Updating Stock March entries from "3 X 1" to "3.1 X 1.1"...');
    
    // Find Stock March entries with "3 X 1" size
    const entries3x1 = await CarpetReceived.find({
      $and: [
        {
          $or: [
            { receiveNo: { $regex: '^H-', $options: 'i' } },
            { receiveNo: { $regex: '^March', $options: 'i' } },
            { weaverName: { $regex: 'stock march', $options: 'i' } }
          ]
        },
        { size: "3 X 1" }
      ]
    });
    
    console.log(`📊 Found ${entries3x1.length} entries to update`);
    
    let updatedCount = 0;
    
    for (const entry of entries3x1) {
      const updateData = {
        size: "3.1 X 1.1"
      };

      // Update issueNo object if it exists
      if (entry.issueNo) {
        updateData['issueNo.size.sizeInYard'] = "3.1 X 1.1";
        updateData['issueNo.size.sizeinMeter'] = "3.1 X 1.1";
      }

      await CarpetReceived.updateOne(
        { _id: entry._id },
        { $set: updateData }
      );

      updatedCount++;
      console.log(`✅ Updated: ${entry.receiveNo} - Size: "3 X 1" -> "3.1 X 1.1"`);
    }
    
    console.log(`🎉 Successfully updated ${updatedCount} entries from "3 X 1" to "3.1 X 1.1"`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

update3x1To31x11();
