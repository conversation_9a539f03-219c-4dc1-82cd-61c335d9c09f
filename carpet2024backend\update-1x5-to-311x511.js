const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function update1x5To311x511() {
  try {
    console.log('🔍 Updating Stock March entries from "1 X 5" to "3.11 X 5.11"...');
    
    // Find Stock March entries with "1 X 5" size
    const entries1x5 = await CarpetReceived.find({
      $and: [
        {
          $or: [
            { receiveNo: { $regex: '^H-', $options: 'i' } },
            { receiveNo: { $regex: '^March', $options: 'i' } },
            { weaverName: { $regex: 'stock march', $options: 'i' } }
          ]
        },
        { size: "1 X 5" }
      ]
    });
    
    console.log(`📊 Found ${entries1x5.length} entries to update`);
    
    let updatedCount = 0;
    
    for (const entry of entries1x5) {
      const updateData = {
        size: "3.11 X 5.11"
      };

      // Update issueNo object if it exists
      if (entry.issueNo) {
        updateData['issueNo.size.sizeInYard'] = "3.11 X 5.11";
        updateData['issueNo.size.sizeinMeter'] = "3.11 X 5.11";
      }

      await CarpetReceived.updateOne(
        { _id: entry._id },
        { $set: updateData }
      );

      updatedCount++;
      console.log(`✅ Updated: ${entry.receiveNo} - Size: "1 X 5" -> "3.11 X 5.11"`);
    }
    
    console.log(`🎉 Successfully updated ${updatedCount} entries from "1 X 5" to "3.11 X 5.11"`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

update1x5To311x511();
