const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function check3x1Entries() {
  try {
    console.log('🔍 Checking Stock March entries with "3 X 1" size...');
    
    // Find Stock March entries with "3 X 1" size
    const entries3x1 = await CarpetReceived.find({
      $and: [
        {
          $or: [
            { receiveNo: { $regex: '^H-', $options: 'i' } },
            { receiveNo: { $regex: '^March', $options: 'i' } },
            { weaverName: { $regex: 'stock march', $options: 'i' } }
          ]
        },
        { size: "3 X 1" }
      ]
    });
    
    console.log(`📊 Found ${entries3x1.length} Stock March entries with "3 X 1" size:`);
    
    for (const entry of entries3x1) {
      console.log(`${entry.receiveNo}: "${entry.size}"`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

check3x1Entries();
