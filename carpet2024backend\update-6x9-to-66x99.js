const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function update6x9To66x99() {
  try {
    console.log('🔍 Updating Stock March entries from "6 X 9" to "6.6 X 9.9"...');
    
    // Find Stock March entries with "6 X 9" size
    const entries6x9 = await CarpetReceived.find({
      $and: [
        {
          $or: [
            { receiveNo: { $regex: '^H-', $options: 'i' } },
            { receiveNo: { $regex: '^March', $options: 'i' } },
            { weaverName: { $regex: 'stock march', $options: 'i' } }
          ]
        },
        { size: "6 X 9" }
      ]
    });
    
    console.log(`📊 Found ${entries6x9.length} entries to update`);
    
    let updatedCount = 0;
    
    for (const entry of entries6x9) {
      const updateData = {
        size: "6.6 X 9.9"
      };

      // Update issueNo object if it exists
      if (entry.issueNo) {
        updateData['issueNo.size.sizeInYard'] = "6.6 X 9.9";
        updateData['issueNo.size.sizeinMeter'] = "6.6 X 9.9";
      }

      await CarpetReceived.updateOne(
        { _id: entry._id },
        { $set: updateData }
      );

      updatedCount++;
      console.log(`✅ Updated: ${entry.receiveNo} - Size: "6 X 9" -> "6.6 X 9.9"`);
    }
    
    console.log(`🎉 Successfully updated ${updatedCount} entries from "6 X 9" to "6.6 X 9.9"`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

update6x9To66x99();
