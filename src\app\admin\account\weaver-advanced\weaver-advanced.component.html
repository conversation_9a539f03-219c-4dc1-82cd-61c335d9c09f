<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">Account</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);"><PERSON></a></li>
            <li class="breadcrumb-item active">Advanced</li>
          </ol>
        </div>
        <h4 class="page-title">Weaver Advanced</h4>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="header-title mb-0"><PERSON> - Advanced</h4>
            <button 
              type="button" 
              class="btn btn-primary"
              (click)="addAdvancedPayment()"
              [disabled]="!advancedForm.get('weaver')?.value">
              <i class="bx bx-plus me-1"></i>Add Advanced
            </button>
          </div>
          
          <!-- Selection Form -->
          <form [formGroup]="advancedForm" class="row g-3 mb-4">
            <div class="col-md-4">
              <label for="weaver" class="form-label">Select Weaver</label>
              <select
                class="form-select"
                id="weaver"
                formControlName="weaver"
                (change)="onFormChange()">
                <option value="">Choose Weaver...</option>
                <option *ngFor="let weaver of weavers" [value]="weaver._id">
                  {{ weaver.code }}-{{ weaver.name }}
                </option>
              </select>
            </div>

            <div class="col-md-4">
              <label for="date" class="form-label">Date</label>
              <input
                type="date"
                class="form-control"
                id="date"
                formControlName="date"
                (change)="onFormChange()">
            </div>
          </form>

          <!-- Payment Entry Form -->
          <div *ngIf="advancedForm.get('weaver')?.value && advancedForm.get('date')?.value" class="card border-primary mb-4">
            <div class="card-header bg-primary text-white">
              <h6 class="mb-0"><i class="bx bx-money me-2"></i>Add Payment Entry</h6>
            </div>
            <div class="card-body">
              <form [formGroup]="paymentEntryForm" class="row g-3">
                <div class="col-md-2">
                  <label for="challanNo" class="form-label">Challan No. *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="challanNo"
                    formControlName="challanNo"
                    placeholder="00001"
                    readonly>
                </div>

                <div class="col-md-2">
                  <label for="paymentMode" class="form-label">Payment Mode *</label>
                  <select
                    class="form-select"
                    id="paymentMode"
                    formControlName="paymentMode">
                    <option value="">Select Mode...</option>
                    <option value="debit">Debit</option>
                    <option value="credit">Credit</option>
                    <option value="payment">Payment</option>
                    <option value="advanced">Advanced</option>
                    <option value="tds">TDS</option>
                    <option value="commission">Commission</option>
                  </select>
                </div>

                <div class="col-md-2">
                  <label for="amount" class="form-label">Amount *</label>
                  <input
                    type="number"
                    class="form-control"
                    id="amount"
                    formControlName="amount"
                    placeholder="Enter amount"
                    min="0"
                    step="0.01">
                </div>

                <div class="col-md-2">
                  <label for="bankAccount" class="form-label">Bank Account *</label>
                  <select
                    class="form-select"
                    id="bankAccount"
                    formControlName="bankAccount">
                    <option value="">Select Account...</option>
                    <option *ngFor="let account of bankAccounts" [value]="account._id">
                      {{ account.bankName }} - {{ account.accountNumber }}
                    </option>
                  </select>
                </div>

                <div class="col-md-2">
                  <label for="remarks" class="form-label">Remarks</label>
                  <input
                    type="text"
                    class="form-control"
                    id="remarks"
                    formControlName="remarks"
                    placeholder="Enter remarks (optional)">
                </div>

                <div class="col-md-2 d-flex align-items-end">
                  <button
                    type="button"
                    class="btn btn-success w-100"
                    [disabled]="!paymentEntryForm.get('amount')?.value || !paymentEntryForm.get('bankAccount')?.value || !paymentEntryForm.get('challanNo')?.value || !paymentEntryForm.get('paymentMode')?.value || isSaving"
                    (click)="savePaymentEntry()">
                    <i class="bx bx-save me-1"></i>
                    <span *ngIf="!isSaving">Save</span>
                    <span *ngIf="isSaving">
                      <span class="spinner-border spinner-border-sm me-1"></span>
                      Saving...
                    </span>
                  </button>
                </div>
              </form>
            </div>
          </div>

          <!-- Loading Spinner -->
          <div *ngIf="isLoading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>

          <!-- Advanced Ledger Table -->
          <div *ngIf="showTable && !isLoading" class="mt-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <h5 class="mb-0">
                Weaver: {{ getSelectedWeaverName() }} |
                Date: {{ advancedForm.get('date')?.value | date:'dd-MM-yyyy' }}
              </h5>
            </div>

            <div class="table-responsive">
              <table class="table table-bordered table-striped">
                <thead class="table-dark">
                  <tr>
                    <th>Sr. No</th>
                    <th>Date</th>
                    <th>Carpet No</th>
                    <th>Credit</th>
                    <th>Debit</th>
                    <th>Balance</th>
                    <th>Remark</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let entry of advancedEntries; let i = index">
                    <td>{{ i + 1 }}</td>
                    <td>{{ entry.date | date:'dd-MM-yyyy' }}</td>
                    <td>{{ entry.carpetNo || '-' }}</td>
                    <td class="text-success text-end">{{ entry.cr > 0 ? (entry.cr | currency:'INR':'symbol':'1.2-2') : '-' }}</td>
                    <td class="text-danger text-end">{{ entry.dr > 0 ? (entry.dr | currency:'INR':'symbol':'1.2-2') : '-' }}</td>
                    <td class="text-end" [class]="entry.balance >= 0 ? 'text-success' : 'text-danger'">
                      {{ entry.balance | currency:'INR':'symbol':'1.2-2' }}
                    </td>
                    <td>{{ entry.remark || '-' }}</td>
                  </tr>
                  
                  <!-- Totals Row -->
                  <tr class="table-warning fw-bold">
                    <td colspan="3" class="text-center">Total</td>
                    <td class="text-end">{{ getTotalCR() | currency:'INR':'symbol':'1.2-2' }}</td>
                    <td class="text-end">{{ getTotalDR() | currency:'INR':'symbol':'1.2-2' }}</td>
                    <td class="text-end" [class.text-danger]="getFinalBalance() < 0" [class.text-success]="getFinalBalance() > 0">
                      {{ getFinalBalance() | currency:'INR':'symbol':'1.2-2' }}
                    </td>
                    <td>-</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- No Data Message -->
            <div *ngIf="advancedEntries.length === 0" class="text-center py-4">
              <div class="alert alert-info">
                <i class="bx bx-info-circle me-2"></i>
                No advanced payment data found for the selected weaver and date range.
              </div>
            </div>
          </div>

          <!-- Instructions -->
          <div *ngIf="!showTable && !isLoading" class="text-center py-4">
            <div class="alert alert-light">
              <i class="bx bx-info-circle me-2"></i>
              Please select weaver and date to view the advanced payment entries.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
