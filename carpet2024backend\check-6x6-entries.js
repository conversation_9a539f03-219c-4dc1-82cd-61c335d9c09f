const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function check6x6Entries() {
  try {
    console.log('🔍 Checking Stock March entries with "6 X 6" size...');
    
    // Find Stock March entries with "6 X 6" size
    const entries6x6 = await CarpetReceived.find({
      $and: [
        {
          $or: [
            { receiveNo: { $regex: '^H-', $options: 'i' } },
            { receiveNo: { $regex: '^March', $options: 'i' } },
            { weaverName: { $regex: 'stock march', $options: 'i' } }
          ]
        },
        { size: "6 X 6" }
      ]
    });
    
    console.log(`📊 Found ${entries6x6.length} Stock March entries with "6 X 6" size:`);
    
    for (const entry of entries6x6) {
      console.log(`${entry.receiveNo}: "${entry.size}"`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

check6x6Entries();
